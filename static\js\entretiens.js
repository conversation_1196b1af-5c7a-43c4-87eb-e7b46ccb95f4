// Gestion des formulaires et modales
document.addEventListener('DOMContentLoaded', function() {
    // Bouton Nouvelle Vidange
    const btnNouvelleVidange = document.getElementById('btnNouvelleVidange');
    const verificationSection = document.getElementById('verificationSection');
    const ajoutVidangeSection = document.getElementById('ajoutVidangeSection');

    if (btnNouvelleVidange) {
        btnNouvelleVidange.addEventListener('click', function() {
            verificationSection.style.display = 'block';
            ajoutVidangeSection.style.display = 'none';
        });
    }

    // Formulaire de vérification
    const verificationForm = document.getElementById('verificationForm');
    if (verificationForm) {
        verificationForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const matricule = document.getElementById('matricule').value;
            verifierVehicule(matricule);
        });
    }

    // Bouton Annuler Vérification
    const btnAnnulerVerification = document.getElementById('btnAnnulerVerification');
    if (btnAnnulerVerification) {
        btnAnnulerVerification.addEventListener('click', function() {
            verificationSection.style.display = 'none';
            document.getElementById('vehiculeInfoDisplay').style.display = 'none';
            document.getElementById('btnConfirmerVehicule').style.display = 'none';
            document.getElementById('matricule').value = '';
        });
    }

    // Bouton Confirmer Véhicule
    const btnConfirmerVehicule = document.getElementById('btnConfirmerVehicule');
    if (btnConfirmerVehicule) {
        btnConfirmerVehicule.addEventListener('click', function() {
            verificationSection.style.display = 'none';
            ajoutVidangeSection.style.display = 'block';
        });
    }

    // Formulaire d'ajout de vidange
    const ajoutVidangeForm = document.getElementById('ajoutVidangeForm');
    if (ajoutVidangeForm) {
        ajoutVidangeForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            ajouterVidange(formData);
        });
    }

    // Gestion des pièces remplacées
    const btnAjouterPiece = document.getElementById('btnAjouterPiece');
    const autrePieceInput = document.getElementById('autrePiece');
    const piecesContainer = document.getElementById('piecesContainer');

    if (btnAjouterPiece && autrePieceInput && piecesContainer) {
        btnAjouterPiece.addEventListener('click', function() {
            const pieceText = autrePieceInput.value.trim();
            if (pieceText) {
                const newId = 'piece' + (piecesContainer.children.length + 1);
                const div = document.createElement('div');
                div.className = 'form-check';
                div.innerHTML = `
                    <input class="form-check-input" type="checkbox" name="pieces_remplacees[]" 
                           value="${pieceText}" id="${newId}" checked>
                    <label class="form-check-label" for="${newId}">${pieceText}</label>
                `;
                piecesContainer.appendChild(div);
                autrePieceInput.value = '';
            }
        });
    }

    // Initialiser les gestionnaires d'événements pour les boutons dans le tableau
    initTableButtons();
});

// Vérification du véhicule
function verifierVehicule(matricule) {
    // Valider le matricule avant l'envoi
    if (!matricule || matricule.trim() === '') {
        alert('Veuillez entrer un matricule');
        return;
    }

    const infoDisplay = document.getElementById('vehiculeInfoDisplay');
    const detailsDisplay = document.getElementById('vehiculeDetailsDisplay');
    const btnConfirmer = document.getElementById('btnConfirmerVehicule');
    const vehiculeIdInput = document.getElementById('vehicule_id_ajout');
    const ajoutVidangeMatricule = document.getElementById('ajoutVidangeMatricule');

    // Afficher un indicateur de chargement
    infoDisplay.style.display = 'block';
    infoDisplay.className = 'alert alert-info mt-3';
    detailsDisplay.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"></div><p>Recherche en cours...</p></div>';

    fetch('/verifier_vehicule?matricule=' + encodeURIComponent(matricule.trim()))
        .then(response => {
            if (!response.ok) {
                throw new Error('Erreur réseau');
            }
            return response.json();
        })
        .then(data => {

            infoDisplay.style.display = 'block';

            if (data.success && data.vehicule) {
                detailsDisplay.innerHTML = `
                    <strong>Matricule:</strong> ${data.vehicule.matricule}<br>
                    <strong>Type:</strong> ${data.vehicule.type_vehicule}<br>
                    <strong>Unité:</strong> ${data.vehicule.unite || 'Non assigné'}<br>
                    <strong>Marque:</strong> ${data.vehicule.marque}
                `;
                btnConfirmer.style.display = 'inline-block';
                vehiculeIdInput.value = data.vehicule.id;
                ajoutVidangeMatricule.textContent = data.vehicule.matricule;

                infoDisplay.className = 'alert alert-success mt-3';
            } else {
                detailsDisplay.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Aucun véhicule trouvé</strong><br>
                        ${data.message || 'Aucun véhicule trouvé avec ce matricule.'}
                    </div>`;
                btnConfirmer.style.display = 'none';
                infoDisplay.className = 'alert alert-danger mt-3';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            detailsDisplay.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong>Erreur</strong><br>
                    Une erreur est survenue lors de la vérification du véhicule. Veuillez réessayer.
                </div>`;
            btnConfirmer.style.display = 'none';
            infoDisplay.className = 'alert alert-danger mt-3';
        });
}

// Ajout d'une vidange
function ajouterVidange(formData) {
    fetch('/entretiens/ajouter', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.ok) {
            window.location.reload();
        } else {
            throw new Error('Erreur lors de l\'ajout de la vidange');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert(error.message);
    });
}

// Mise à jour du kilométrage
function mettreAJourKilometrage(vehiculeId) {
    const updateSection = document.getElementById('updateKilometrageSection');
    if (updateSection) {
        document.getElementById('vehicule_id_update').value = vehiculeId;
        updateSection.style.display = 'block';
        window.scrollTo({ top: updateSection.offsetTop, behavior: 'smooth' });
    }
}

// Supprimer un entretien
function supprimerEntretien(entretienId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cet entretien ?')) {
        fetch(`/entretiens/supprimer/${entretienId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                throw new Error(data.error || 'Erreur lors de la suppression');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Erreur lors de la suppression de l\'entretien');
        });
    }
}

// Initialisation des boutons du tableau
function initTableButtons() {
    // Gestion de la mise à jour du kilométrage
    const formMiseAJourKilometrage = document.getElementById('formMiseAJourKilometrage');
    if (formMiseAJourKilometrage) {
        formMiseAJourKilometrage.addEventListener('submit', function(e) {
            e.preventDefault();
            const vehiculeId = document.getElementById('vehicule_id_update').value;
            const kilometrage = document.getElementById('nouveau_kilometrage').value;

            fetch(`/mise_a_jour_kilometrage/${vehiculeId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `kilometrage=${kilometrage}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    throw new Error(data.error || 'Erreur lors de la mise à jour');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Erreur lors de la mise à jour du kilométrage');
            });
        });
    }

    // Gestion du bouton d'annulation de mise à jour du kilométrage
    const btnCancelKilometrage = document.getElementById('btnCancelKilometrage');
    if (btnCancelKilometrage) {
        btnCancelKilometrage.addEventListener('click', function() {
            document.getElementById('updateKilometrageSection').style.display = 'none';
        });
    }

    // Gestionnaire d'événements pour les boutons du tableau
    document.querySelector('#vidangesTable tbody').addEventListener('click', function(e) {
        const target = e.target.closest('button');
        if (!target) return;

        if (target.classList.contains('btn-update-km')) {
            const vehiculeId = target.getAttribute('data-vehicule-id');
            mettreAJourKilometrage(vehiculeId);
        } else if (target.classList.contains('btn-delete-entretien')) {
            const entretienId = target.getAttribute('data-entretien-id');
            supprimerEntretien(entretienId);
        }
    });
}

// Enhanced Futuristic Military Alert System
function checkNotification(kilometrageActuel, kilometrageProchainEntretien, vehiculeMatricule = '') {
    const alertIcon = document.getElementById('notificationIcon');
    const notificationBadge = document.getElementById('notificationBadge');
    const difference = kilometrageProchainEntretien - kilometrageActuel;

    if (difference <= 1000) {
        // Activation de l'icône avec effet d'apparition
        alertIcon.style.display = 'flex';

        // Délai pour l'animation d'apparition
        setTimeout(() => {
            alertIcon.classList.add('active');
        }, 100);

        // Configuration du message avec plus de détails
        const urgencyLevel = difference <= 200 ? 'CRITIQUE' : difference <= 500 ? 'URGENT' : 'ATTENTION';
        const message = `${urgencyLevel}: ${vehiculeMatricule ? vehiculeMatricule + ' - ' : ''}${difference} km restants`;
        alertIcon.setAttribute('data-bs-title', message);

        // Gestion des niveaux d'urgence
        if (difference <= 200) {
            // Niveau critique
            alertIcon.classList.add('urgent', 'critical');
            notificationBadge.style.display = 'flex';
            notificationBadge.querySelector('.badge-text').textContent = '!';

            // Vibration si supportée
            if (navigator.vibrate) {
                navigator.vibrate([200, 100, 200]);
            }

            // Son d'alerte critique
            playNotificationSound('critical');

        } else if (difference <= 500) {
            // Niveau urgent
            alertIcon.classList.add('urgent');
            alertIcon.classList.remove('critical');
            notificationBadge.style.display = 'flex';
            notificationBadge.querySelector('.badge-text').textContent = difference;

            // Son d'alerte normal
            playNotificationSound('urgent');

        } else {
            // Niveau attention
            alertIcon.classList.remove('urgent', 'critical');
            notificationBadge.style.display = 'none';

            // Son d'information
            playNotificationSound('info');
        }

        // Mise à jour du SVG selon l'urgence
        updateIconGradient(difference);

        // Animation des particules selon l'urgence
        animateParticles(difference <= 500);

    } else {
        // Désactivation avec animation de sortie
        alertIcon.classList.remove('active', 'urgent', 'critical');
        notificationBadge.style.display = 'none';

        setTimeout(() => {
            alertIcon.style.display = 'none';
        }, 600);
    }
}

// Fonction pour jouer les sons de notification
function playNotificationSound(type = 'info') {
    const audio = document.getElementById('notificationSound');
    if (audio && !document.querySelector('#toggleSound')?.classList.contains('muted')) {
        // Ajustement du volume selon le type
        switch(type) {
            case 'critical':
                audio.volume = 0.8;
                break;
            case 'urgent':
                audio.volume = 0.6;
                break;
            default:
                audio.volume = 0.4;
        }

        audio.currentTime = 0;
        audio.play().catch(e => console.log('Audio play failed:', e));
    }
}

// Fonction pour mettre à jour le gradient de l'icône
function updateIconGradient(difference) {
    const iconPath = document.querySelector('.icon-path');
    if (iconPath) {
        if (difference <= 200) {
            iconPath.style.fill = 'url(#urgentGradient)';
        } else if (difference <= 500) {
            iconPath.style.fill = 'url(#urgentGradient)';
        } else {
            iconPath.style.fill = 'url(#iconGradient)';
        }
    }
}

// Fonction pour animer les particules
function animateParticles(isUrgent) {
    const particles = document.querySelectorAll('.particle');
    particles.forEach((particle) => {
        if (isUrgent) {
            particle.style.animationDuration = '2s';
            particle.style.background = 'radial-gradient(circle, #ff6b6b, transparent)';
        } else {
            particle.style.animationDuration = '4s';
            particle.style.background = 'radial-gradient(circle, #00ff9d, transparent)';
        }
    });
}

// Fonction pour créer des effets de particules dynamiques
function createDynamicParticles() {
    const iconWrapper = document.querySelector('.icon-wrapper');
    if (!iconWrapper) return;

    for (let i = 0; i < 3; i++) {
        setTimeout(() => {
            const particle = document.createElement('div');
            particle.className = 'dynamic-particle';
            particle.style.cssText = `
                position: absolute;
                width: 2px;
                height: 2px;
                background: radial-gradient(circle, #00ff9d, transparent);
                border-radius: 50%;
                pointer-events: none;
                z-index: 4;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                animation: dynamicFloat 3s ease-out forwards;
            `;

            iconWrapper.appendChild(particle);

            // Suppression après animation
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 3000);
        }, i * 500);
    }
}

// Animation pour les particules dynamiques
const dynamicParticleStyle = document.createElement('style');
dynamicParticleStyle.textContent = `
    @keyframes dynamicFloat {
        0% {
            transform: translateY(0) scale(0);
            opacity: 0;
        }
        20% {
            transform: translateY(-15px) scale(1);
            opacity: 1;
        }
        80% {
            transform: translateY(-30px) scale(1);
            opacity: 0.8;
        }
        100% {
            transform: translateY(-50px) scale(0);
            opacity: 0;
        }
    }
`;
document.head.appendChild(dynamicParticleStyle);

// Initialize advanced futuristic alert system
document.addEventListener('DOMContentLoaded', function() {
    const alertIcon = document.getElementById('notificationIcon');

    if (alertIcon) {
        // Initialize Bootstrap tooltip with enhanced styling
        new bootstrap.Tooltip(alertIcon, {
            placement: 'bottom',
            trigger: 'hover focus',
            animation: true,
            delay: { show: 200, hide: 100 },
            template: `
                <div class="tooltip military-tooltip" role="tooltip">
                    <div class="tooltip-arrow"></div>
                    <div class="tooltip-inner" style="
                        background: linear-gradient(145deg, #E8F0E0, #C8D4B8);
                        color: #2A3D12;
                        border: 1px solid rgba(125, 140, 101, 0.3);
                        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                        border-radius: 8px;
                        font-weight: 600;
                        font-size: 12px;
                        backdrop-filter: blur(10px);
                    ">
                    </div>
                </div>
            `
        });

        // Gestionnaire de clic pour interactions avancées
        alertIcon.addEventListener('click', function(e) {
            e.preventDefault();
            handleNotificationClick();
        });

        // Gestionnaire de survol pour effets dynamiques
        alertIcon.addEventListener('mouseenter', function() {
            createDynamicParticles();
            this.style.transform = 'translateY(-3px) scale(1.05)';
        });

        alertIcon.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });

        // Simulation avec données d'exemple (à remplacer par vraies données)
        simulateNotificationDemo();
    }
});

// Fonction pour gérer le clic sur la notification
function handleNotificationClick() {
    const alertIcon = document.getElementById('notificationIcon');

    // Animation de clic
    alertIcon.style.transform = 'scale(0.95)';
    setTimeout(() => {
        alertIcon.style.transform = '';
    }, 150);

    // Affichage du toast avec détails
    showDetailedNotification();

    // Création d'effets visuels
    createClickEffect();
}

// Fonction pour afficher une notification détaillée
function showDetailedNotification() {
    const toastBody = document.getElementById('toastVidangesBody');
    if (toastBody) {
        toastBody.innerHTML = `
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <i class="fas fa-exclamation-triangle text-warning"></i>
                </div>
                <div>
                    <strong>Entretien requis</strong><br>
                    <small class="text-muted">Cliquez pour voir les détails</small>
                </div>
            </div>
        `;
        const toast = new bootstrap.Toast(document.getElementById('toastVidanges'), {
            delay: 5000
        });
        toast.show();
    }
}

// Fonction pour créer un effet de clic
function createClickEffect() {
    const alertIcon = document.getElementById('notificationIcon');
    const rect = alertIcon.getBoundingClientRect();

    // Création d'ondulations au clic
    for (let i = 0; i < 3; i++) {
        setTimeout(() => {
            const ripple = document.createElement('div');
            ripple.style.cssText = `
                position: fixed;
                left: ${rect.left + rect.width/2}px;
                top: ${rect.top + rect.height/2}px;
                width: 4px;
                height: 4px;
                background: radial-gradient(circle, rgba(168,192,144,0.6), transparent);
                border-radius: 50%;
                pointer-events: none;
                z-index: 9999;
                animation: clickRipple 1s ease-out forwards;
                transform: translate(-50%, -50%);
            `;

            document.body.appendChild(ripple);

            setTimeout(() => {
                if (ripple.parentNode) {
                    ripple.parentNode.removeChild(ripple);
                }
            }, 1000);
        }, i * 100);
    }
}

// Animation pour l'effet de clic
const clickEffectStyle = document.createElement('style');
clickEffectStyle.textContent = `
    @keyframes clickRipple {
        0% {
            transform: translate(-50%, -50%) scale(1);
            opacity: 0.8;
        }
        100% {
            transform: translate(-50%, -50%) scale(20);
            opacity: 0;
        }
    }
`;
document.head.appendChild(clickEffectStyle);

// Fonction d'intégration avec le système avancé
function integrateWithAdvancedSystem() {
    // Attendre que le système avancé soit initialisé
    if (window.advancedNotificationSystem) {
        console.log('Integration with Advanced Notification System successful');

        // Exemple d'utilisation avec données réelles
        // Cette fonction sera appelée avec les vraies données du serveur
        window.checkAdvancedNotification = function(kilometrageActuel, kilometrageProchain, vehiculeMatricule) {
            const difference = kilometrageProchain - kilometrageActuel;
            let urgencyLevel = 'none';
            let message = '';

            if (difference <= 200) {
                urgencyLevel = 'critical';
                message = `CRITIQUE: ${vehiculeMatricule} - ${difference} km restants`;
            } else if (difference <= 500) {
                urgencyLevel = 'urgent';
                message = `URGENT: ${vehiculeMatricule} - ${difference} km restants`;
            } else if (difference <= 1000) {
                urgencyLevel = 'medium';
                message = `ATTENTION: ${vehiculeMatricule} - ${difference} km restants`;
            }

            if (urgencyLevel !== 'none') {
                window.advancedNotificationSystem.updateNotification(urgencyLevel, message, {
                    vehicule: vehiculeMatricule,
                    kilometrageActuel,
                    kilometrageProchain,
                    difference
                });
            }
        };

        // Fonction pour désactiver les notifications
        window.hideAdvancedNotification = function() {
            const alertIcon = document.getElementById('notificationIcon');
            if (alertIcon) {
                alertIcon.classList.remove('active', 'urgent', 'critical', 'medium');
                setTimeout(() => {
                    alertIcon.style.display = 'none';
                }, 600);
            }
        };

    } else {
        // Réessayer après 100ms si le système n'est pas encore prêt
        setTimeout(integrateWithAdvancedSystem, 100);
    }
}

// Fonction de démonstration améliorée
function simulateNotificationDemo() {
    // Attendre l'intégration
    integrateWithAdvancedSystem();

    // Simulation progressive des niveaux d'urgence
    setTimeout(() => {
        if (window.checkAdvancedNotification) {
            window.checkAdvancedNotification(4700, 5200, 'ABC-123'); // Attention
        }
    }, 2000);

    setTimeout(() => {
        if (window.checkAdvancedNotification) {
            window.checkAdvancedNotification(4600, 5000, 'DEF-456'); // Urgent
        }
    }, 5000);

    setTimeout(() => {
        if (window.checkAdvancedNotification) {
            window.checkAdvancedNotification(4850, 5000, 'GHI-789'); // Critique
        }
    }, 8000);
}
