/* Advanced Futuristic Notification Icon Styles */

/* Styles responsifs pour l'icône */
@media (max-width: 768px) {
    .military-alert-icon {
        width: 48px;
        height: 48px;
    }
    
    .alert-icon {
        width: 26px;
        height: 26px;
    }
    
    .notification-badge {
        width: 16px;
        height: 16px;
        top: -3px;
        right: -3px;
    }
    
    .badge-text {
        font-size: 9px;
    }
}

/* Mode sombre */
@media (prefers-color-scheme: dark) {
    .military-alert-icon {
        background: 
            linear-gradient(145deg, #2A3D12, #1F2D0E),
            radial-gradient(circle at 30% 30%, rgba(168,192,144,0.2), transparent 50%);
        box-shadow: 
            -6px -6px 12px rgba(255, 255, 255, 0.1),
            6px 6px 12px rgba(0, 0, 0, 0.5),
            inset 2px 2px 4px rgba(255, 255, 255, 0.1),
            0 0 20px rgba(168, 192, 144, 0.2);
    }
    
    .military-alert-icon::before {
        background: linear-gradient(
            135deg,
            rgba(255,255,255,0.2) 0%,
            rgba(255,255,255,0.1) 30%,
            transparent 50%,
            rgba(255,255,255,0.05) 70%,
            transparent 100%
        );
    }
}

/* Animations de performance optimisées */
.military-alert-icon {
    will-change: transform, box-shadow;
    backface-visibility: hidden;
    perspective: 1000px;
}

.wave-ring {
    will-change: transform, opacity;
}

.particle {
    will-change: transform, opacity;
}

/* Effets de focus pour l'accessibilité */
.military-alert-icon:focus {
    outline: 2px solid rgba(168, 192, 144, 0.6);
    outline-offset: 4px;
}

.military-alert-icon:focus-visible {
    box-shadow: 
        -6px -6px 12px rgba(255, 255, 255, 0.9),
        6px 6px 12px rgba(128, 144, 96, 0.4),
        inset 2px 2px 4px rgba(255, 255, 255, 0.5),
        0 0 30px rgba(168, 192, 144, 0.6),
        0 0 0 3px rgba(168, 192, 144, 0.3);
}

/* États d'interaction avancés */
.military-alert-icon.clicked {
    animation: clickFeedback 0.3s ease-out;
}

@keyframes clickFeedback {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.95);
    }
    100% {
        transform: scale(1);
    }
}

/* Effet de pulsation pour les notifications critiques */
.military-alert-icon.critical {
    animation: criticalPulse 1s infinite ease-in-out;
}

@keyframes criticalPulse {
    0%, 100% {
        transform: scale(1);
        filter: brightness(1);
    }
    50% {
        transform: scale(1.05);
        filter: brightness(1.2);
    }
}

/* Amélioration des particules avec GPU */
.particle {
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Effet de brillance amélioré */
.military-alert-icon.enhanced-shine::after {
    animation-duration: 2s;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255,255,255,0.4) 45%,
        rgba(255,255,255,0.6) 50%,
        rgba(255,255,255,0.4) 55%,
        transparent 100%
    );
}

/* Indicateurs de niveau d'urgence */
.military-alert-icon[data-urgency="low"] .wave-ring {
    border-color: rgba(168, 192, 144, 0.3);
    animation-duration: 4s;
}

.military-alert-icon[data-urgency="medium"] .wave-ring {
    border-color: rgba(255, 193, 7, 0.5);
    animation-duration: 2.5s;
}

.military-alert-icon[data-urgency="high"] .wave-ring {
    border-color: rgba(255, 107, 107, 0.7);
    animation-duration: 1.5s;
}

.military-alert-icon[data-urgency="critical"] .wave-ring {
    border-color: rgba(220, 53, 69, 0.8);
    animation-duration: 1s;
}

/* Tooltip personnalisé amélioré */
.military-tooltip .tooltip-inner {
    position: relative;
    overflow: hidden;
}

.military-tooltip .tooltip-inner::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255,255,255,0.2) 50%,
        transparent 100%
    );
    animation: tooltipShine 2s infinite;
}

@keyframes tooltipShine {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Effets sonores visuels */
.military-alert-icon.sound-wave {
    position: relative;
}

.military-alert-icon.sound-wave::before {
    content: '';
    position: absolute;
    inset: -10px;
    border: 2px solid rgba(168, 192, 144, 0.3);
    border-radius: 50%;
    animation: soundWave 1s ease-out;
}

@keyframes soundWave {
    0% {
        transform: scale(0.8);
        opacity: 0.8;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

/* Préchargement des animations */
.military-alert-icon * {
    animation-fill-mode: both;
}

/* Optimisations de performance */
.military-alert-icon,
.wave-ring,
.particle,
.status-indicator {
    transform: translateZ(0);
    will-change: transform;
}

/* Réduction de mouvement pour l'accessibilité */
@media (prefers-reduced-motion: reduce) {
    .military-alert-icon,
    .wave-ring,
    .particle,
    .status-indicator {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .military-alert-icon:hover {
        transform: none !important;
    }
}

/* États de chargement */
.military-alert-icon.loading {
    opacity: 0.7;
    pointer-events: none;
}

.military-alert-icon.loading .alert-icon {
    animation: loadingRotate 1s linear infinite;
}

@keyframes loadingRotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
