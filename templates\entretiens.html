{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col">
            <h2>Gestion des Vidanges</h2>
        </div>
        <div class="col-auto d-flex align-items-center">
            <button class="btn btn-primary" id="btnNouvelleVidange">
                <i class="fas fa-plus"></i> Nouvelle Vidange
            </button>
            <div class="military-alert-icon ms-3" id="notificationIcon" style="display: none;" data-bs-toggle="tooltip">
                <div class="icon-wrapper">
                    <svg class="alert-icon" viewBox="0 0 24 24" width="28" height="28">
                        <defs>
                            <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#A8C090"/>
                                <stop offset="100%" style="stop-color:#7D8C65"/>
                            </linearGradient>
                        </defs>
                        <path class="icon-path" d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.9 2 2 2zm6-6v-5c0-3.07-1.63-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.64 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2zm-2 1H8v-6c0-2.48 1.51-4.5 4-4.5s4 2.02 4 4.5v6z"/>
                    </svg>
                    <div class="status-indicator"></div>
                    <div class="effect-layer"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Barre de recherche -->
    <div class="card mb-4">
        <div class="card-body bg-light py-3">
            <form action="{{ url_for('entretiens') }}" method="get" class="d-flex">
                <div class="input-group">
                    <input type="text" name="search_matricule" class="form-control" placeholder="Rechercher les entretiens par matricule..." value="{{ search_matricule }}" aria-label="Rechercher par matricule">
                    <button class="btn btn-outline-primary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                    {% if search_matricule %}
                    <a href="{{ url_for('entretiens') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Effacer
                    </a>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>

    <!-- Section de vérification du véhicule -->
    <div id="verificationSection" class="card mb-4" style="display: none;">
        <div class="card-header">
            <h5 class="mb-0">Vérification du Véhicule</h5>
        </div>
        <div class="card-body">
            <form id="verificationForm">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="matricule">Matricule</label>
                            <input type="text" class="form-control" id="matricule" name="matricule" required>
                        </div>
                    </div>
                </div>
                <div id="vehiculeInfoDisplay" class="alert alert-info mt-3" style="display: none;">
                    <h6>Informations du Véhicule</h6>
                    <p id="vehiculeDetailsDisplay"></p>
                </div>
                <div class="mt-3">
                    <button type="submit" class="btn btn-primary" id="btnVerifierVehicule">Vérifier</button>
                    <button type="button" class="btn btn-success" id="btnConfirmerVehicule" style="display: none;">Confirmer et Ajouter Vidange</button>
                    <button type="button" class="btn btn-secondary" id="btnAnnulerVerification">Annuler</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Section d'ajout de vidange -->
    <div id="ajoutVidangeSection" class="card mb-4" style="display: none;">
        <div class="card-header">
            <h5 class="mb-0">Nouvelle Vidange pour Véhicule <span id="ajoutVidangeMatricule"></span></h5>
        </div>
        <div class="card-body">
            <form id="ajoutVidangeForm" action="{{ url_for('ajouter_entretien') }}" method="POST">
                <input type="hidden" id="vehicule_id_ajout" name="vehicule_id">
                <input type="hidden" name="type_entretien" value="Vidange">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="date_entretien">Date de Vidange</label>
                            <input type="date" class="form-control" id="date_entretien" name="date_entretien" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="kilometrage">Kilométrage Actuel</label>
                            <input type="number" class="form-control" id="kilometrage" name="kilometrage" required>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="kilometrage_prochain">Prochain Kilométrage</label>
                            <input type="number" class="form-control" id="kilometrage_prochain" name="kilometrage_prochain" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Pièces Remplacées</label>
                            <div id="piecesContainer">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="pieces_remplacees[]" value="Filtre à huile" id="piece1">
                                    <label class="form-check-label" for="piece1">Filtre à huile</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="pieces_remplacees[]" value="Filtre à air" id="piece2">
                                    <label class="form-check-label" for="piece2">Filtre à air</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="pieces_remplacees[]" value="Filtre à carburant" id="piece3">
                                    <label class="form-check-label" for="piece3">Filtre à carburant</label>
                                </div>
                            </div>
                            <div class="mt-2">
                                <input type="text" class="form-control" id="autrePiece" placeholder="Autre pièce...">
                                <button type="button" class="btn btn-sm btn-secondary mt-2" id="btnAjouterPiece">Ajouter</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="form-group">
                            <label for="description">Description/Commentaires (optionnel)</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="Ajoutez des commentaires sur cette vidange..."></textarea>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <button type="submit" class="btn btn-primary">Enregistrer</button>
                    <button type="button" class="btn btn-secondary" id="btnAnnulerAjout">Annuler</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Tableau des vidanges -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped" id="vidangesTable">
                    <thead>
                        <tr>
                            <th>Matricule</th>
                            <th>Unité</th>
                            <th>Date</th>
                            <th>Kilométrage</th>
                            <th>Prochain Kilométrage</th>
                            <th>Pièces Remplacées</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for entretien in entretiens %}
                        <tr>
                            <td>{{ entretien.vehicule.matricule }}</td>
                            <td>{{ entretien.vehicule.unite }}</td>
                            <td>{{ entretien.date_entretien.strftime('%d/%m/%Y') }}</td>
                            <td>{{ entretien.kilometrage }}</td>
                            <td>{{ entretien.kilometrage_prochain }}</td>
                            <td>{{ entretien.pieces_remplacees or 'Aucune' }}</td>
                            <td>
                                <button class="btn btn-sm btn-info btn-update-km" data-vehicule-id="{{ entretien.vehicule.id }}">
                                    <i class="fas fa-edit"></i> Mettre à jour km
                                </button>
                                <button class="btn btn-sm btn-danger btn-delete-entretien" data-entretien-id="{{ entretien.id }}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Section de mise à jour du kilométrage -->
<div id="updateKilometrageSection" class="card mb-4" style="display: none;">
    <div class="card-header text-white" style="background-color: #4b5320;">
        <h5 class="mb-0">Mise à jour du kilométrage</h5>
    </div>
    <div class="card-body">
        <form id="formMiseAJourKilometrage" class="row g-3 align-items-end">
            <input type="hidden" id="vehicule_id_update" name="vehicule_id">
            <div class="col-md-6">
                <label for="nouveau_kilometrage" class="form-label">Nouveau kilométrage</label>
                <div class="input-group">
                    <input type="number" 
                           class="form-control form-control-lg" 
                           id="nouveau_kilometrage" 
                           name="kilometrage" 
                           required 
                           min="0" 
                           step="1"
                           placeholder="Entrez le kilométrage actuel">
                    <span class="input-group-text">km</span>
                </div>
                <div class="form-text">Le kilométrage doit être supérieur au précédent</div>
                <div id="kilometrageFeedback" class="invalid-feedback">
                    Veuillez entrer un kilométrage valide
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-grid gap-2 d-md-flex">
                    <button type="submit" class="btn btn-primary" id="btnSaveKilometrage">
                        <i class="fas fa-save me-1"></i> Enregistrer
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="btnCancelKilometrage">
                        <i class="fas fa-times me-1"></i> Annuler
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Toast pour les notifications -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div id="toastVidanges" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto">Vidanges à venir</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="toastVidangesBody">
        </div>
    </div>
</div>

<style>
.military-alert-icon {
    position: relative;
    cursor: pointer;
    width: 44px;
    height: 44px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 14px;
    background: linear-gradient(145deg, #E8F0E0, #C8D4B8);
    box-shadow: 
        -4px -4px 8px rgba(255, 255, 255, 0.8),
        4px 4px 8px rgba(128, 144, 96, 0.3),
        inset 1px 1px 1px rgba(255, 255, 255, 0.4);
}

.icon-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: inherit;
    overflow: hidden;
    background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0));
}

.alert-icon {
    position: relative;
    z-index: 2;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.icon-path {
    fill: url(#iconGradient);
    transition: all 0.3s ease;
}

.status-indicator {
    position: absolute;
    top: 6px;
    right: 6px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: linear-gradient(145deg, #98B475, #7D8C65);
    box-shadow: 
        0 0 6px rgba(125, 140, 101, 0.5),
        inset -1px -1px 2px rgba(0,0,0,0.1);
    transform: scale(0);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.effect-layer {
    position: absolute;
    inset: 0;
    background: radial-gradient(
        circle at center,
        rgba(255,255,255,0.1) 0%,
        transparent 70%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
}

/* États actifs et animations */
.military-alert-icon.active {
    animation: alertAppear 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.military-alert-icon.active .status-indicator {
    transform: scale(1);
    animation: statusPulse 2s infinite;
}

.military-alert-icon.urgent {
    background: linear-gradient(145deg, #FFE5D9, #FFD0B5);
}

.military-alert-icon.urgent .icon-path {
    fill: #D17451;
}

.military-alert-icon.urgent .status-indicator {
    background: linear-gradient(145deg, #FF6B6B, #D63E3E);
    box-shadow: 0 0 8px rgba(255, 107, 107, 0.6);
}

.military-alert-icon:hover {
    transform: translateY(-2px);
    box-shadow: 
        -6px -6px 12px rgba(255, 255, 255, 0.8),
        6px 6px 12px rgba(128, 144, 96, 0.4),
        inset 1px 1px 1px rgba(255, 255, 255, 0.4);
}

.military-alert-icon:hover .effect-layer {
    opacity: 1;
}

/* Animations */
@keyframes alertAppear {
    0% {
        transform: scale(0.9) translateY(10px);
        opacity: 0;
    }
    100% {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

@keyframes statusPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 6px rgba(125, 140, 101, 0.5);
    }
    50% {
        transform: scale(1.2);
        box-shadow: 0 0 12px rgba(125, 140, 101, 0.7);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 6px rgba(125, 140, 101, 0.5);
    }
}

/* Effet de brillance */
.military-alert-icon::before {
    content: '';
    position: absolute;
    inset: 1px;
    border-radius: inherit;
    background: linear-gradient(
        135deg,
        rgba(255,255,255,0.5) 0%,
        rgba(255,255,255,0.2) 50%,
        transparent 100%
    );
    opacity: 0.7;
    pointer-events: none;
}

/* Effet de surbrillance au hover */
.military-alert-icon:hover::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        45deg,
        transparent 0%,
        rgba(255,255,255,0.2) 50%,
        transparent 100%
    );
    animation: lightSweep 2s infinite;
    border-radius: inherit;
}

@keyframes lightSweep {
    0% {
        background-position: -100% -100%;
    }
    100% {
        background-position: 200% 200%;
    }
}
</style>

<!-- Ajout du son de notification -->
<audio id="notificationSound" preload="auto">
    <source src="{{ url_for('static', filename='sounds/alert.mp3') }}" type="audio/mp3">
</audio>

{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/entretiens.js') }}"></script>
{% endblock %}
{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded and parsed. Initializing entretien page scripts.');

    // Vérification de l'existence des éléments
    const btnNouvelleVidange = document.getElementById('btnNouvelleVidange');
    const verificationSection = document.getElementById('verificationSection');
    const ajoutVidangeSection = document.getElementById('ajoutVidangeSection');
    const vidangesTableBody = document.querySelector('#vidangesTable tbody');
    const btnConfirmerVehicule = document.getElementById('btnConfirmerVehicule');
    const btnAnnulerVerification = document.getElementById('btnAnnulerVerification');
    const btnAnnulerAjout = document.getElementById('btnAnnulerAjout');
    const btnAjouterPiece = document.getElementById('btnAjouterPiece');
    const formMiseAJourKilometrage = document.getElementById('formMiseAJourKilometrage');
    const btnCancelKilometrage = document.getElementById('btnCancelKilometrage');

    // Gestionnaire pour le bouton Nouvelle Vidange
    if (btnNouvelleVidange) {
        btnNouvelleVidange.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Nouvelle Vidange button clicked');
            verificationSection.style.display = 'block';
            ajoutVidangeSection.style.display = 'none';
        });
    }

    // Gestionnaire pour le bouton Annuler de la vérification
    if (btnAnnulerVerification) {
        btnAnnulerVerification.addEventListener('click', function() {
            verificationSection.style.display = 'none';
            document.getElementById('verificationForm').reset();
            document.getElementById('vehiculeInfoDisplay').style.display = 'none';
        });
    }

    // Gestionnaire pour le bouton Confirmer Véhicule
    if (btnConfirmerVehicule) {
        btnConfirmerVehicule.addEventListener('click', function() {
            const matricule = document.getElementById('matricule').value;
            fetch(`/verifier_vehicule?matricule=${encodeURIComponent(matricule)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.vehicule) {
                        document.getElementById('vehicule_id_ajout').value = data.vehicule.id;
                        document.getElementById('ajoutVidangeMatricule').textContent = data.vehicule.matricule;
                        verificationSection.style.display = 'none';
                        ajoutVidangeSection.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Une erreur est survenue');
                });
        });
    }

    // Gestionnaire pour le bouton Annuler de l'ajout
    if (btnAnnulerAjout) {
        btnAnnulerAjout.addEventListener('click', function() {
            ajoutVidangeSection.style.display = 'none';
            document.getElementById('ajoutVidangeForm').reset();
        });
    }

    // Gestionnaire pour l'ajout de pièces
    if (btnAjouterPiece) {
        btnAjouterPiece.addEventListener('click', function() {
            const autrePiece = document.getElementById('autrePiece').value.trim();
            if (autrePiece) {
                const piecesContainer = document.getElementById('piecesContainer');
                const newPiece = document.createElement('div');
                newPiece.className = 'form-check';
                newPiece.innerHTML = `
                    <input class="form-check-input" type="checkbox" name="pieces_remplacees[]" value="${autrePiece}" id="piece${piecesContainer.children.length + 1}" checked>
                    <label class="form-check-label" for="piece${piecesContainer.children.length + 1}">${autrePiece}</label>
                `;
                piecesContainer.appendChild(newPiece);
                document.getElementById('autrePiece').value = '';
            }
        });
    }

    // Gestionnaire pour le formulaire de mise à jour du kilométrage
    if (formMiseAJourKilometrage) {
        formMiseAJourKilometrage.addEventListener('submit', function(e) {
            e.preventDefault();
            const vehiculeId = document.getElementById('vehicule_id_update').value;
            const nouveauKilometrage = document.getElementById('nouveau_kilometrage').value;

            fetch('/mise_a_jour_kilometrage', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    vehicule_id: vehiculeId,
                    kilometrage: nouveauKilometrage
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    alert(data.message || 'Erreur lors de la mise à jour du kilométrage');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Une erreur est survenue');
            });
        });
    }

    // Gestionnaire pour le bouton Annuler de la mise à jour du kilométrage
    if (btnCancelKilometrage) {
        btnCancelKilometrage.addEventListener('click', function() {
            document.getElementById('updateKilometrageSection').style.display = 'none';
            document.getElementById('formMiseAJourKilometrage').reset();
        });
    }

    // Gestionnaire pour les boutons de mise à jour et suppression
    if (vidangesTableBody) {
        vidangesTableBody.addEventListener('click', function(e) {
            const target = e.target.closest('button');
            
            if (target) {
                if (target.classList.contains('btn-update-km')) {
                    const vehiculeId = target.getAttribute('data-vehicule-id');
                    const row = target.closest('tr');
                    const currentKm = row.cells[3].textContent.trim();
                    mettreAJourKilometrage(vehiculeId, currentKm);
                } else if (target.classList.contains('btn-delete-entretien')) {
                    const entretienId = target.getAttribute('data-entretien-id');
                    supprimerEntretien(entretienId);
                }
            }
        });
    }
});

// Fonction pour mettre à jour le kilométrage
function mettreAJourKilometrage(vehiculeId, currentKm) {
    console.log('Updating kilometrage for vehicle:', vehiculeId, 'Current km:', currentKm);
    const kmInput = document.getElementById('nouveau_kilometrage');
    const vehiculeIdInput = document.getElementById('vehicule_id_update');
    
    if (kmInput && vehiculeIdInput) {
        vehiculeIdInput.value = vehiculeId;
        kmInput.value = currentKm;
        document.getElementById('updateKilometrageSection').style.display = 'block';
    }
}

// Fonction pour supprimer un entretien
function supprimerEntretien(entretienId) {
    console.log('Deleting entretien:', entretienId);
    if (confirm('Êtes-vous sûr de vouloir supprimer cette vidange ?')) {
        fetch(`/entretiens/supprimer/${entretienId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then data => {
            if (data.success) {
                window.location.reload();
            } else {
                throw new Error(data.message || 'Erreur lors de la suppression');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Une erreur est survenue lors de la suppression');
        });
    }
}

// Gestionnaire pour le son de notification
document.getElementById('toggleSound').addEventListener('click', function() {
    const audio = document.getElementById('notificationSound');
    if (audio) {
        if (this.classList.toggle('muted')) {
            audio.pause();
        } else {
            audio.play();
        }
    }
});

// Fonction pour afficher la notification
function afficherNotification(message) {
    const toastBody = document.getElementById('toastVidangesBody');
    if (toastBody) {
        toastBody.textContent = message;
        const toast = new bootstrap.Toast(document.getElementById('toastVidanges'));
        toast.show();
    }
}

// Exemple d'utilisation de la notification
afficherNotification('Nouveau entretien ajouté pour le véhicule ABC123');
</script>
{% endblock %}
