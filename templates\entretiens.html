{% extends "base.html" %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/advanced-notification.css') }}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col">
            <h2>Gestion des Vidanges</h2>
        </div>
        <div class="col-auto d-flex align-items-center">
            <button class="btn btn-primary" id="btnNouvelleVidange">
                <i class="fas fa-plus"></i> Nouvelle Vidange
            </button>
            <div class="military-alert-icon ms-3" id="notificationIcon" style="display: none;" data-bs-toggle="tooltip">
                <div class="icon-wrapper">
                    <!-- Cercles d'onde animés -->
                    <div class="wave-ring wave-ring-1"></div>
                    <div class="wave-ring wave-ring-2"></div>
                    <div class="wave-ring wave-ring-3"></div>

                    <!-- Particules flottantes -->
                    <div class="floating-particles">
                        <div class="particle particle-1"></div>
                        <div class="particle particle-2"></div>
                        <div class="particle particle-3"></div>
                        <div class="particle particle-4"></div>
                    </div>

                    <!-- SVG principal avec animations -->
                    <svg class="alert-icon" viewBox="0 0 24 24" width="32" height="32">
                        <defs>
                            <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#A8C090">
                                    <animate attributeName="stop-color"
                                             values="#A8C090;#B8D0A0;#A8C090"
                                             dur="3s"
                                             repeatCount="indefinite"/>
                                </stop>
                                <stop offset="100%" style="stop-color:#7D8C65">
                                    <animate attributeName="stop-color"
                                             values="#7D8C65;#8D9C75;#7D8C65"
                                             dur="3s"
                                             repeatCount="indefinite"/>
                                </stop>
                            </linearGradient>

                            <linearGradient id="urgentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#FF6B6B">
                                    <animate attributeName="stop-color"
                                             values="#FF6B6B;#FF8E8E;#FF6B6B"
                                             dur="1.5s"
                                             repeatCount="indefinite"/>
                                </stop>
                                <stop offset="100%" style="stop-color:#D63E3E">
                                    <animate attributeName="stop-color"
                                             values="#D63E3E;#E65E5E;#D63E3E"
                                             dur="1.5s"
                                             repeatCount="indefinite"/>
                                </stop>
                            </linearGradient>

                            <!-- Filtre de brillance -->
                            <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
                                <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                                <feMerge>
                                    <feMergeNode in="coloredBlur"/>
                                    <feMergeNode in="SourceGraphic"/>
                                </feMerge>
                            </filter>

                            <!-- Masque pour effet de scan -->
                            <mask id="scanMask">
                                <rect width="100%" height="100%" fill="white"/>
                                <rect width="100%" height="2" fill="black" y="0">
                                    <animateTransform attributeName="transform"
                                                      type="translate"
                                                      values="0,-2;0,26;0,-2"
                                                      dur="2s"
                                                      repeatCount="indefinite"/>
                                </rect>
                            </mask>
                        </defs>

                        <!-- Icône principale avec masque de scan -->
                        <g mask="url(#scanMask)">
                            <path class="icon-path"
                                  d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.9 2 2 2zm6-6v-5c0-3.07-1.63-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.64 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2zm-2 1H8v-6c0-2.48 1.51-4.5 4-4.5s4 2.02 4 4.5v6z"
                                  filter="url(#glow)">
                                <animateTransform attributeName="transform"
                                                  type="scale"
                                                  values="1;1.05;1"
                                                  dur="2s"
                                                  repeatCount="indefinite"/>
                            </path>
                        </g>

                        <!-- Lignes de scan horizontales -->
                        <g class="scan-lines" opacity="0.3">
                            <line x1="2" y1="6" x2="22" y2="6" stroke="#00ff9d" stroke-width="0.5">
                                <animate attributeName="opacity" values="0;1;0" dur="1.5s" repeatCount="indefinite"/>
                            </line>
                            <line x1="2" y1="12" x2="22" y2="12" stroke="#00ff9d" stroke-width="0.5">
                                <animate attributeName="opacity" values="0;1;0" dur="1.5s" begin="0.5s" repeatCount="indefinite"/>
                            </line>
                            <line x1="2" y1="18" x2="22" y2="18" stroke="#00ff9d" stroke-width="0.5">
                                <animate attributeName="opacity" values="0;1;0" dur="1.5s" begin="1s" repeatCount="indefinite"/>
                            </line>
                        </g>
                    </svg>

                    <!-- Indicateur de statut amélioré -->
                    <div class="status-indicator">
                        <div class="status-core"></div>
                        <div class="status-ring"></div>
                    </div>

                    <!-- Couche d'effets holographiques -->
                    <div class="holographic-layer"></div>

                    <!-- Effet de brillance mobile -->
                    <div class="shine-effect"></div>

                    <!-- Badge de compteur -->
                    <div class="notification-badge" id="notificationBadge" style="display: none;">
                        <span class="badge-text">!</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Barre de recherche -->
    <div class="card mb-4">
        <div class="card-body bg-light py-3">
            <form action="{{ url_for('entretiens') }}" method="get" class="d-flex">
                <div class="input-group">
                    <input type="text" name="search_matricule" class="form-control" placeholder="Rechercher les entretiens par matricule..." value="{{ search_matricule }}" aria-label="Rechercher par matricule">
                    <button class="btn btn-outline-primary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                    {% if search_matricule %}
                    <a href="{{ url_for('entretiens') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Effacer
                    </a>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>

    <!-- Section de vérification du véhicule -->
    <div id="verificationSection" class="card mb-4" style="display: none;">
        <div class="card-header">
            <h5 class="mb-0">Vérification du Véhicule</h5>
        </div>
        <div class="card-body">
            <form id="verificationForm">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="matricule">Matricule</label>
                            <input type="text" class="form-control" id="matricule" name="matricule" required>
                        </div>
                    </div>
                </div>
                <div id="vehiculeInfoDisplay" class="alert alert-info mt-3" style="display: none;">
                    <h6>Informations du Véhicule</h6>
                    <p id="vehiculeDetailsDisplay"></p>
                </div>
                <div class="mt-3">
                    <button type="submit" class="btn btn-primary" id="btnVerifierVehicule">Vérifier</button>
                    <button type="button" class="btn btn-success" id="btnConfirmerVehicule" style="display: none;">Confirmer et Ajouter Vidange</button>
                    <button type="button" class="btn btn-secondary" id="btnAnnulerVerification">Annuler</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Section d'ajout de vidange -->
    <div id="ajoutVidangeSection" class="card mb-4" style="display: none;">
        <div class="card-header">
            <h5 class="mb-0">Nouvelle Vidange pour Véhicule <span id="ajoutVidangeMatricule"></span></h5>
        </div>
        <div class="card-body">
            <form id="ajoutVidangeForm" action="{{ url_for('ajouter_entretien') }}" method="POST">
                <input type="hidden" id="vehicule_id_ajout" name="vehicule_id">
                <input type="hidden" name="type_entretien" value="Vidange">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="date_entretien">Date de Vidange</label>
                            <input type="date" class="form-control" id="date_entretien" name="date_entretien" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="kilometrage">Kilométrage Actuel</label>
                            <input type="number" class="form-control" id="kilometrage" name="kilometrage" required>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="kilometrage_prochain">Prochain Kilométrage</label>
                            <input type="number" class="form-control" id="kilometrage_prochain" name="kilometrage_prochain" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Pièces Remplacées</label>
                            <div id="piecesContainer">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="pieces_remplacees[]" value="Filtre à huile" id="piece1">
                                    <label class="form-check-label" for="piece1">Filtre à huile</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="pieces_remplacees[]" value="Filtre à air" id="piece2">
                                    <label class="form-check-label" for="piece2">Filtre à air</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="pieces_remplacees[]" value="Filtre à carburant" id="piece3">
                                    <label class="form-check-label" for="piece3">Filtre à carburant</label>
                                </div>
                            </div>
                            <div class="mt-2">
                                <input type="text" class="form-control" id="autrePiece" placeholder="Autre pièce...">
                                <button type="button" class="btn btn-sm btn-secondary mt-2" id="btnAjouterPiece">Ajouter</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="form-group">
                            <label for="description">Description/Commentaires (optionnel)</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="Ajoutez des commentaires sur cette vidange..."></textarea>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <button type="submit" class="btn btn-primary">Enregistrer</button>
                    <button type="button" class="btn btn-secondary" id="btnAnnulerAjout">Annuler</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Tableau des vidanges -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped" id="vidangesTable">
                    <thead>
                        <tr>
                            <th>Matricule</th>
                            <th>Unité</th>
                            <th>Date</th>
                            <th>Kilométrage</th>
                            <th>Prochain Kilométrage</th>
                            <th>Pièces Remplacées</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for entretien in entretiens %}
                        <tr>
                            <td>{{ entretien.vehicule.matricule }}</td>
                            <td>{{ entretien.vehicule.unite }}</td>
                            <td>{{ entretien.date_entretien.strftime('%d/%m/%Y') }}</td>
                            <td>{{ entretien.kilometrage }}</td>
                            <td>{{ entretien.kilometrage_prochain }}</td>
                            <td>{{ entretien.pieces_remplacees or 'Aucune' }}</td>
                            <td>
                                <button class="btn btn-sm btn-info btn-update-km" data-vehicule-id="{{ entretien.vehicule.id }}">
                                    <i class="fas fa-edit"></i> Mettre à jour km
                                </button>
                                <button class="btn btn-sm btn-danger btn-delete-entretien" data-entretien-id="{{ entretien.id }}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Section de mise à jour du kilométrage -->
<div id="updateKilometrageSection" class="card mb-4" style="display: none;">
    <div class="card-header text-white" style="background-color: #4b5320;">
        <h5 class="mb-0">Mise à jour du kilométrage</h5>
    </div>
    <div class="card-body">
        <form id="formMiseAJourKilometrage" class="row g-3 align-items-end">
            <input type="hidden" id="vehicule_id_update" name="vehicule_id">
            <div class="col-md-6">
                <label for="nouveau_kilometrage" class="form-label">Nouveau kilométrage</label>
                <div class="input-group">
                    <input type="number" 
                           class="form-control form-control-lg" 
                           id="nouveau_kilometrage" 
                           name="kilometrage" 
                           required 
                           min="0" 
                           step="1"
                           placeholder="Entrez le kilométrage actuel">
                    <span class="input-group-text">km</span>
                </div>
                <div class="form-text">Le kilométrage doit être supérieur au précédent</div>
                <div id="kilometrageFeedback" class="invalid-feedback">
                    Veuillez entrer un kilométrage valide
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-grid gap-2 d-md-flex">
                    <button type="submit" class="btn btn-primary" id="btnSaveKilometrage">
                        <i class="fas fa-save me-1"></i> Enregistrer
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="btnCancelKilometrage">
                        <i class="fas fa-times me-1"></i> Annuler
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Toast pour les notifications -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div id="toastVidanges" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto">Vidanges à venir</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="toastVidangesBody">
        </div>
    </div>
</div>

<style>
/* Icône principale - Design futuriste */
.military-alert-icon {
    position: relative;
    cursor: pointer;
    width: 56px;
    height: 56px;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 18px;
    background:
        linear-gradient(145deg, #E8F0E0, #C8D4B8),
        radial-gradient(circle at 30% 30%, rgba(255,255,255,0.3), transparent 50%);
    box-shadow:
        -6px -6px 12px rgba(255, 255, 255, 0.9),
        6px 6px 12px rgba(128, 144, 96, 0.4),
        inset 2px 2px 4px rgba(255, 255, 255, 0.5),
        0 0 20px rgba(168, 192, 144, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.icon-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: inherit;
    overflow: visible;
    background:
        linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0)),
        radial-gradient(circle at center, rgba(0,255,157,0.05), transparent 60%);
}

/* Cercles d'onde animés */
.wave-ring {
    position: absolute;
    border: 2px solid rgba(168, 192, 144, 0.4);
    border-radius: 50%;
    animation: waveExpand 3s infinite ease-out;
    opacity: 0;
}

.wave-ring-1 {
    width: 60px;
    height: 60px;
    animation-delay: 0s;
}

.wave-ring-2 {
    width: 80px;
    height: 80px;
    animation-delay: 1s;
}

.wave-ring-3 {
    width: 100px;
    height: 100px;
    animation-delay: 2s;
}

/* Particules flottantes */
.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 3px;
    height: 3px;
    background: radial-gradient(circle, #00ff9d, transparent);
    border-radius: 50%;
    opacity: 0;
    animation: floatParticle 4s infinite ease-in-out;
}

.particle-1 {
    top: 20%;
    left: 15%;
    animation-delay: 0s;
}

.particle-2 {
    top: 70%;
    right: 20%;
    animation-delay: 1s;
}

.particle-3 {
    bottom: 25%;
    left: 25%;
    animation-delay: 2s;
}

.particle-4 {
    top: 40%;
    right: 15%;
    animation-delay: 3s;
}

/* SVG et icône principale */
.alert-icon {
    position: relative;
    z-index: 5;
    filter:
        drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15))
        drop-shadow(0 0 10px rgba(168, 192, 144, 0.3));
    transition: all 0.4s ease;
}

.icon-path {
    fill: url(#iconGradient);
    transition: all 0.4s ease;
    stroke: rgba(255, 255, 255, 0.1);
    stroke-width: 0.5;
}

/* Indicateur de statut amélioré */
.status-indicator {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    transform: scale(0);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    z-index: 6;
}

.status-core {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(145deg, #98B475, #7D8C65);
    box-shadow:
        0 0 8px rgba(125, 140, 101, 0.6),
        inset -2px -2px 4px rgba(0,0,0,0.1),
        inset 2px 2px 4px rgba(255,255,255,0.3);
}

.status-ring {
    position: absolute;
    width: 120%;
    height: 120%;
    top: -10%;
    left: -10%;
    border: 1px solid rgba(152, 180, 117, 0.5);
    border-radius: 50%;
    animation: statusRingPulse 2s infinite ease-in-out;
}

/* Couche holographique */
.holographic-layer {
    position: absolute;
    inset: 2px;
    border-radius: inherit;
    background:
        linear-gradient(45deg,
            transparent 0%,
            rgba(0,255,157,0.1) 25%,
            transparent 50%,
            rgba(168,192,144,0.1) 75%,
            transparent 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    animation: holographicShift 6s infinite linear;
}

/* Effet de brillance mobile */
.shine-effect {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        45deg,
        transparent 0%,
        transparent 40%,
        rgba(255,255,255,0.3) 50%,
        transparent 60%,
        transparent 100%
    );
    transform: rotate(45deg);
    opacity: 0;
    transition: all 0.6s ease;
}

/* Badge de notification */
.notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 18px;
    height: 18px;
    background: linear-gradient(145deg, #FF4757, #FF3742);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow:
        0 2px 8px rgba(255, 71, 87, 0.4),
        inset 1px 1px 2px rgba(255,255,255,0.3);
    z-index: 7;
    animation: badgeBounce 2s infinite ease-in-out;
}

.badge-text {
    color: white;
    font-size: 10px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

/* États actifs et animations */
.military-alert-icon.active {
    animation: alertAppear 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow:
        -6px -6px 12px rgba(255, 255, 255, 0.9),
        6px 6px 12px rgba(128, 144, 96, 0.4),
        inset 2px 2px 4px rgba(255, 255, 255, 0.5),
        0 0 30px rgba(168, 192, 144, 0.5);
}

.military-alert-icon.active .status-indicator {
    transform: scale(1);
}

.military-alert-icon.active .holographic-layer {
    opacity: 1;
}

.military-alert-icon.active .wave-ring {
    animation-play-state: running;
}

.military-alert-icon.urgent {
    background:
        linear-gradient(145deg, #FFE5D9, #FFD0B5),
        radial-gradient(circle at 30% 30%, rgba(255,107,107,0.2), transparent 50%);
    box-shadow:
        -6px -6px 12px rgba(255, 255, 255, 0.9),
        6px 6px 12px rgba(255, 107, 107, 0.3),
        inset 2px 2px 4px rgba(255, 255, 255, 0.5),
        0 0 25px rgba(255, 107, 107, 0.4);
    animation: urgentPulse 1.5s infinite ease-in-out;
}

.military-alert-icon.urgent .icon-path {
    fill: url(#urgentGradient);
}

.military-alert-icon.urgent .status-core {
    background: linear-gradient(145deg, #FF6B6B, #D63E3E);
    box-shadow:
        0 0 12px rgba(255, 107, 107, 0.8),
        inset -2px -2px 4px rgba(0,0,0,0.2),
        inset 2px 2px 4px rgba(255,255,255,0.3);
}

.military-alert-icon.urgent .wave-ring {
    border-color: rgba(255, 107, 107, 0.6);
    animation-duration: 1.5s;
}

.military-alert-icon:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow:
        -8px -8px 16px rgba(255, 255, 255, 0.9),
        8px 8px 16px rgba(128, 144, 96, 0.5),
        inset 2px 2px 4px rgba(255, 255, 255, 0.6),
        0 0 40px rgba(168, 192, 144, 0.6);
}

.military-alert-icon:hover .shine-effect {
    opacity: 1;
    animation: shineMove 1.5s ease-in-out;
}

.military-alert-icon:hover .alert-icon {
    transform: scale(1.1);
}

.military-alert-icon:hover .holographic-layer {
    opacity: 0.8;
}

/* Animations principales */
@keyframes alertAppear {
    0% {
        transform: scale(0.8) translateY(15px) rotate(-5deg);
        opacity: 0;
        filter: blur(5px);
    }
    50% {
        transform: scale(1.1) translateY(-5px) rotate(2deg);
        opacity: 0.8;
        filter: blur(1px);
    }
    100% {
        transform: scale(1) translateY(0) rotate(0deg);
        opacity: 1;
        filter: blur(0);
    }
}

@keyframes waveExpand {
    0% {
        transform: scale(0.8);
        opacity: 0.8;
    }
    50% {
        opacity: 0.4;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

@keyframes floatParticle {
    0% {
        transform: translateY(0) scale(0);
        opacity: 0;
    }
    20% {
        transform: translateY(-10px) scale(1);
        opacity: 1;
    }
    80% {
        transform: translateY(-20px) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateY(-30px) scale(0);
        opacity: 0;
    }
}

@keyframes statusRingPulse {
    0% {
        transform: scale(1);
        opacity: 0.6;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.3;
    }
    100% {
        transform: scale(1);
        opacity: 0.6;
    }
}

@keyframes holographicShift {
    0% {
        background-position: 0% 0%;
    }
    25% {
        background-position: 100% 0%;
    }
    50% {
        background-position: 100% 100%;
    }
    75% {
        background-position: 0% 100%;
    }
    100% {
        background-position: 0% 0%;
    }
}

@keyframes shineMove {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }
    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
    }
}

@keyframes badgeBounce {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
}

@keyframes urgentPulse {
    0% {
        box-shadow:
            -6px -6px 12px rgba(255, 255, 255, 0.9),
            6px 6px 12px rgba(255, 107, 107, 0.3),
            inset 2px 2px 4px rgba(255, 255, 255, 0.5),
            0 0 25px rgba(255, 107, 107, 0.4);
    }
    50% {
        box-shadow:
            -6px -6px 12px rgba(255, 255, 255, 0.9),
            6px 6px 12px rgba(255, 107, 107, 0.5),
            inset 2px 2px 4px rgba(255, 255, 255, 0.5),
            0 0 35px rgba(255, 107, 107, 0.7);
    }
    100% {
        box-shadow:
            -6px -6px 12px rgba(255, 255, 255, 0.9),
            6px 6px 12px rgba(255, 107, 107, 0.3),
            inset 2px 2px 4px rgba(255, 255, 255, 0.5),
            0 0 25px rgba(255, 107, 107, 0.4);
    }
}

/* Effet de brillance permanent */
.military-alert-icon::before {
    content: '';
    position: absolute;
    inset: 2px;
    border-radius: inherit;
    background: linear-gradient(
        135deg,
        rgba(255,255,255,0.6) 0%,
        rgba(255,255,255,0.3) 30%,
        transparent 50%,
        rgba(255,255,255,0.1) 70%,
        transparent 100%
    );
    opacity: 0.8;
    pointer-events: none;
    z-index: 1;
}

/* Effet de scan futuriste */
.military-alert-icon::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(0,255,157,0.1) 45%,
        rgba(0,255,157,0.3) 50%,
        rgba(0,255,157,0.1) 55%,
        transparent 100%
    );
    transform: translateX(-100%);
    animation: scanLine 3s infinite ease-in-out;
    pointer-events: none;
    z-index: 3;
}

@keyframes scanLine {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}
</style>

<!-- Ajout du son de notification -->
<audio id="notificationSound" preload="auto">
    <source src="{{ url_for('static', filename='sounds/alert.mp3') }}" type="audio/mp3">
</audio>

{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/entretiens.js') }}"></script>
<script src="{{ url_for('static', filename='js/advanced-notification.js') }}"></script>
<script src="{{ url_for('static', filename='js/notification-demo.js') }}"></script>
{% endblock %}
{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded and parsed. Initializing entretien page scripts.');

    // Vérification de l'existence des éléments
    const btnNouvelleVidange = document.getElementById('btnNouvelleVidange');
    const verificationSection = document.getElementById('verificationSection');
    const ajoutVidangeSection = document.getElementById('ajoutVidangeSection');
    const vidangesTableBody = document.querySelector('#vidangesTable tbody');
    const btnConfirmerVehicule = document.getElementById('btnConfirmerVehicule');
    const btnAnnulerVerification = document.getElementById('btnAnnulerVerification');
    const btnAnnulerAjout = document.getElementById('btnAnnulerAjout');
    const btnAjouterPiece = document.getElementById('btnAjouterPiece');
    const formMiseAJourKilometrage = document.getElementById('formMiseAJourKilometrage');
    const btnCancelKilometrage = document.getElementById('btnCancelKilometrage');

    // Gestionnaire pour le bouton Nouvelle Vidange
    if (btnNouvelleVidange) {
        btnNouvelleVidange.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Nouvelle Vidange button clicked');
            verificationSection.style.display = 'block';
            ajoutVidangeSection.style.display = 'none';
        });
    }

    // Gestionnaire pour le bouton Annuler de la vérification
    if (btnAnnulerVerification) {
        btnAnnulerVerification.addEventListener('click', function() {
            verificationSection.style.display = 'none';
            document.getElementById('verificationForm').reset();
            document.getElementById('vehiculeInfoDisplay').style.display = 'none';
        });
    }

    // Gestionnaire pour le bouton Confirmer Véhicule
    if (btnConfirmerVehicule) {
        btnConfirmerVehicule.addEventListener('click', function() {
            const matricule = document.getElementById('matricule').value;
            fetch(`/verifier_vehicule?matricule=${encodeURIComponent(matricule)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.vehicule) {
                        document.getElementById('vehicule_id_ajout').value = data.vehicule.id;
                        document.getElementById('ajoutVidangeMatricule').textContent = data.vehicule.matricule;
                        verificationSection.style.display = 'none';
                        ajoutVidangeSection.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Une erreur est survenue');
                });
        });
    }

    // Gestionnaire pour le bouton Annuler de l'ajout
    if (btnAnnulerAjout) {
        btnAnnulerAjout.addEventListener('click', function() {
            ajoutVidangeSection.style.display = 'none';
            document.getElementById('ajoutVidangeForm').reset();
        });
    }

    // Gestionnaire pour l'ajout de pièces
    if (btnAjouterPiece) {
        btnAjouterPiece.addEventListener('click', function() {
            const autrePiece = document.getElementById('autrePiece').value.trim();
            if (autrePiece) {
                const piecesContainer = document.getElementById('piecesContainer');
                const newPiece = document.createElement('div');
                newPiece.className = 'form-check';
                newPiece.innerHTML = `
                    <input class="form-check-input" type="checkbox" name="pieces_remplacees[]" value="${autrePiece}" id="piece${piecesContainer.children.length + 1}" checked>
                    <label class="form-check-label" for="piece${piecesContainer.children.length + 1}">${autrePiece}</label>
                `;
                piecesContainer.appendChild(newPiece);
                document.getElementById('autrePiece').value = '';
            }
        });
    }

    // Gestionnaire pour le formulaire de mise à jour du kilométrage
    if (formMiseAJourKilometrage) {
        formMiseAJourKilometrage.addEventListener('submit', function(e) {
            e.preventDefault();
            const vehiculeId = document.getElementById('vehicule_id_update').value;
            const nouveauKilometrage = document.getElementById('nouveau_kilometrage').value;

            fetch('/mise_a_jour_kilometrage', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    vehicule_id: vehiculeId,
                    kilometrage: nouveauKilometrage
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    alert(data.message || 'Erreur lors de la mise à jour du kilométrage');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Une erreur est survenue');
            });
        });
    }

    // Gestionnaire pour le bouton Annuler de la mise à jour du kilométrage
    if (btnCancelKilometrage) {
        btnCancelKilometrage.addEventListener('click', function() {
            document.getElementById('updateKilometrageSection').style.display = 'none';
            document.getElementById('formMiseAJourKilometrage').reset();
        });
    }

    // Gestionnaire pour les boutons de mise à jour et suppression
    if (vidangesTableBody) {
        vidangesTableBody.addEventListener('click', function(e) {
            const target = e.target.closest('button');
            
            if (target) {
                if (target.classList.contains('btn-update-km')) {
                    const vehiculeId = target.getAttribute('data-vehicule-id');
                    const row = target.closest('tr');
                    const currentKm = row.cells[3].textContent.trim();
                    mettreAJourKilometrage(vehiculeId, currentKm);
                } else if (target.classList.contains('btn-delete-entretien')) {
                    const entretienId = target.getAttribute('data-entretien-id');
                    supprimerEntretien(entretienId);
                }
            }
        });
    }
});

// Fonction pour mettre à jour le kilométrage
function mettreAJourKilometrage(vehiculeId, currentKm) {
    console.log('Updating kilometrage for vehicle:', vehiculeId, 'Current km:', currentKm);
    const kmInput = document.getElementById('nouveau_kilometrage');
    const vehiculeIdInput = document.getElementById('vehicule_id_update');
    
    if (kmInput && vehiculeIdInput) {
        vehiculeIdInput.value = vehiculeId;
        kmInput.value = currentKm;
        document.getElementById('updateKilometrageSection').style.display = 'block';
    }
}

// Fonction pour supprimer un entretien
function supprimerEntretien(entretienId) {
    console.log('Deleting entretien:', entretienId);
    if (confirm('Êtes-vous sûr de vouloir supprimer cette vidange ?')) {
        fetch(`/entretiens/supprimer/${entretienId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                throw new Error(data.message || 'Erreur lors de la suppression');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Une erreur est survenue lors de la suppression');
        });
    }
}

// Gestionnaire pour le son de notification
document.getElementById('toggleSound').addEventListener('click', function() {
    const audio = document.getElementById('notificationSound');
    if (audio) {
        if (this.classList.toggle('muted')) {
            audio.pause();
        } else {
            audio.play();
        }
    }
});

// Fonction pour afficher la notification
function afficherNotification(message) {
    const toastBody = document.getElementById('toastVidangesBody');
    if (toastBody) {
        toastBody.textContent = message;
        const toast = new bootstrap.Toast(document.getElementById('toastVidanges'));
        toast.show();
    }
}

// Exemple d'utilisation de la notification
afficherNotification('Nouveau entretien ajouté pour le véhicule ABC123');
</script>
{% endblock %}
