// Advanced Notification System - Enhanced Features

class AdvancedNotificationSystem {
    constructor() {
        this.alertIcon = document.getElementById('notificationIcon');
        this.isInitialized = false;
        this.currentUrgencyLevel = 'none';
        this.notificationQueue = [];
        this.soundEnabled = true;
        
        this.init();
    }
    
    init() {
        if (!this.alertIcon || this.isInitialized) return;
        
        this.setupEventListeners();
        this.setupIntersectionObserver();
        this.setupPerformanceOptimizations();
        this.isInitialized = true;
        
        console.log('Advanced Notification System initialized');
    }
    
    setupEventListeners() {
        // Gestionnaire de clic avec feedback haptique
        this.alertIcon.addEventListener('click', (e) => {
            e.preventDefault();
            this.handleClick();
        });
        
        // Gestionnaires de survol améliorés
        this.alertIcon.addEventListener('mouseenter', () => {
            this.handleMouseEnter();
        });
        
        this.alertIcon.addEventListener('mouseleave', () => {
            this.handleMouseLeave();
        });
        
        // Support du clavier pour l'accessibilité
        this.alertIcon.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.handleClick();
            }
        });
        
        // Gestionnaire de focus
        this.alertIcon.addEventListener('focus', () => {
            this.alertIcon.classList.add('focused');
        });
        
        this.alertIcon.addEventListener('blur', () => {
            this.alertIcon.classList.remove('focused');
        });
    }
    
    setupIntersectionObserver() {
        // Observer pour optimiser les animations quand l'icône est visible
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.alertIcon.classList.add('in-viewport');
                } else {
                    this.alertIcon.classList.remove('in-viewport');
                }
            });
        }, { threshold: 0.1 });
        
        observer.observe(this.alertIcon);
    }
    
    setupPerformanceOptimizations() {
        // Préchargement des animations critiques
        this.alertIcon.style.willChange = 'transform, opacity';
        
        // Optimisation pour les appareils à faible performance
        if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
            this.alertIcon.classList.add('low-performance');
        }
    }
    
    handleClick() {
        // Animation de feedback
        this.alertIcon.classList.add('clicked');
        setTimeout(() => {
            this.alertIcon.classList.remove('clicked');
        }, 300);
        
        // Vibration haptique si supportée
        if (navigator.vibrate) {
            const pattern = this.getVibrationPattern();
            navigator.vibrate(pattern);
        }
        
        // Effet sonore
        this.playClickSound();
        
        // Effet visuel de clic
        this.createClickEffect();
        
        // Affichage des détails
        this.showNotificationDetails();
    }
    
    handleMouseEnter() {
        this.alertIcon.classList.add('hovered');
        this.createHoverParticles();
        
        // Son de survol subtil
        this.playHoverSound();
    }
    
    handleMouseLeave() {
        this.alertIcon.classList.remove('hovered');
    }
    
    getVibrationPattern() {
        switch(this.currentUrgencyLevel) {
            case 'critical':
                return [200, 100, 200, 100, 200];
            case 'urgent':
                return [150, 100, 150];
            case 'medium':
                return [100, 50, 100];
            default:
                return [50];
        }
    }
    
    playClickSound() {
        if (!this.soundEnabled) return;
        
        // Création d'un son synthétique pour le clic
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);
        
        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.1);
    }
    
    playHoverSound() {
        if (!this.soundEnabled) return;
        
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
        gainNode.gain.setValueAtTime(0.05, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.05);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.05);
    }
    
    createClickEffect() {
        const rect = this.alertIcon.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        // Création de multiples ondulations
        for (let i = 0; i < 5; i++) {
            setTimeout(() => {
                this.createRipple(centerX, centerY, i);
            }, i * 50);
        }
    }
    
    createRipple(x, y, index) {
        const ripple = document.createElement('div');
        const size = 20 + (index * 10);
        const delay = index * 0.1;
        
        ripple.style.cssText = `
            position: fixed;
            left: ${x}px;
            top: ${y}px;
            width: ${size}px;
            height: ${size}px;
            background: radial-gradient(circle, rgba(168,192,144,${0.6 - index * 0.1}), transparent);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            transform: translate(-50%, -50%) scale(0);
            animation: rippleExpand 0.8s ease-out ${delay}s forwards;
        `;
        
        document.body.appendChild(ripple);
        
        setTimeout(() => {
            if (ripple.parentNode) {
                ripple.parentNode.removeChild(ripple);
            }
        }, 800 + (delay * 1000));
    }
    
    createHoverParticles() {
        const rect = this.alertIcon.getBoundingClientRect();
        
        for (let i = 0; i < 3; i++) {
            setTimeout(() => {
                const particle = document.createElement('div');
                const x = rect.left + Math.random() * rect.width;
                const y = rect.top + Math.random() * rect.height;
                
                particle.style.cssText = `
                    position: fixed;
                    left: ${x}px;
                    top: ${y}px;
                    width: 3px;
                    height: 3px;
                    background: radial-gradient(circle, #00ff9d, transparent);
                    border-radius: 50%;
                    pointer-events: none;
                    z-index: 9998;
                    animation: hoverParticleFloat 2s ease-out forwards;
                `;
                
                document.body.appendChild(particle);
                
                setTimeout(() => {
                    if (particle.parentNode) {
                        particle.parentNode.removeChild(particle);
                    }
                }, 2000);
            }, i * 200);
        }
    }
    
    showNotificationDetails() {
        // Affichage d'un modal ou toast avec détails complets
        const modal = this.createDetailModal();
        document.body.appendChild(modal);
        
        // Animation d'apparition
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
        
        // Auto-fermeture après 5 secondes
        setTimeout(() => {
            this.closeDetailModal(modal);
        }, 5000);
    }
    
    createDetailModal() {
        const modal = document.createElement('div');
        modal.className = 'notification-detail-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h5>Détails de la notification</h5>
                    <button type="button" class="close-btn" onclick="this.closest('.notification-detail-modal').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="urgency-indicator ${this.currentUrgencyLevel}"></div>
                    <p>Niveau d'urgence: <strong>${this.getUrgencyText()}</strong></p>
                    <p>Action recommandée: Planifier l'entretien</p>
                </div>
            </div>
        `;
        
        return modal;
    }
    
    closeDetailModal(modal) {
        modal.classList.add('hide');
        setTimeout(() => {
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        }, 300);
    }
    
    getUrgencyText() {
        switch(this.currentUrgencyLevel) {
            case 'critical': return 'CRITIQUE';
            case 'urgent': return 'URGENT';
            case 'medium': return 'ATTENTION';
            default: return 'INFORMATION';
        }
    }
    
    // Méthode publique pour mettre à jour la notification
    updateNotification(urgencyLevel, message, data = {}) {
        this.currentUrgencyLevel = urgencyLevel;
        this.alertIcon.setAttribute('data-urgency', urgencyLevel);
        this.alertIcon.setAttribute('data-bs-title', message);
        
        // Mise à jour visuelle selon l'urgence
        this.updateVisualState(urgencyLevel);
    }
    
    updateVisualState(urgencyLevel) {
        // Suppression des classes précédentes
        this.alertIcon.classList.remove('low', 'medium', 'urgent', 'critical');
        
        // Ajout de la nouvelle classe
        this.alertIcon.classList.add(urgencyLevel);
        
        // Animation d'apparition si nécessaire
        if (this.alertIcon.style.display === 'none') {
            this.alertIcon.style.display = 'flex';
            setTimeout(() => {
                this.alertIcon.classList.add('active');
            }, 100);
        }
    }
    
    // Méthode pour désactiver/activer le son
    toggleSound() {
        this.soundEnabled = !this.soundEnabled;
        return this.soundEnabled;
    }
}

// Styles CSS pour les animations supplémentaires
const additionalStyles = document.createElement('style');
additionalStyles.textContent = `
    @keyframes rippleExpand {
        to {
            transform: translate(-50%, -50%) scale(4);
            opacity: 0;
        }
    }
    
    @keyframes hoverParticleFloat {
        0% {
            transform: translateY(0) scale(0);
            opacity: 0;
        }
        20% {
            transform: translateY(-10px) scale(1);
            opacity: 1;
        }
        100% {
            transform: translateY(-30px) scale(0);
            opacity: 0;
        }
    }
    
    .notification-detail-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .notification-detail-modal.show {
        opacity: 1;
    }
    
    .notification-detail-modal.hide {
        opacity: 0;
    }
    
    .modal-content {
        background: white;
        border-radius: 12px;
        padding: 20px;
        max-width: 400px;
        width: 90%;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        transform: scale(0.9);
        transition: transform 0.3s ease;
    }
    
    .notification-detail-modal.show .modal-content {
        transform: scale(1);
    }
    
    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }
    
    .close-btn {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #999;
    }
    
    .urgency-indicator {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 10px;
    }
    
    .urgency-indicator.critical {
        background: #dc3545;
        box-shadow: 0 0 10px rgba(220,53,69,0.5);
    }
    
    .urgency-indicator.urgent {
        background: #fd7e14;
        box-shadow: 0 0 10px rgba(253,126,20,0.5);
    }
    
    .urgency-indicator.medium {
        background: #ffc107;
        box-shadow: 0 0 10px rgba(255,193,7,0.5);
    }
`;
document.head.appendChild(additionalStyles);

// Initialisation automatique
document.addEventListener('DOMContentLoaded', function() {
    window.advancedNotificationSystem = new AdvancedNotificationSystem();
});
