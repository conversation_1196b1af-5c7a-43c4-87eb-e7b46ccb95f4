// Démonstration des fonctionnalités avancées de notification

class NotificationDemo {
    constructor() {
        this.demoInterval = null;
        this.currentDemo = 0;
        this.demos = [
            {
                name: 'Notification Normale',
                km: 4200,
                nextKm: 5000,
                vehicule: 'ABC-123',
                description: 'Notification standard avec 800km restants'
            },
            {
                name: 'Notification Attention',
                km: 4500,
                nextKm: 5200,
                vehicule: 'DEF-456',
                description: 'Niveau attention avec 700km restants'
            },
            {
                name: 'Notification Urgente',
                km: 4600,
                nextKm: 5000,
                vehicule: 'GHI-789',
                description: 'Niveau urgent avec 400km restants'
            },
            {
                name: 'Notification Critique',
                km: 4850,
                nextKm: 5000,
                vehicule: 'JKL-012',
                description: 'Niveau critique avec 150km restants'
            }
        ];
        
        this.init();
    }
    
    init() {
        this.createDemoControls();
        this.setupEventListeners();
    }
    
    createDemoControls() {
        // Vérifier si les contrôles existent déjà
        if (document.getElementById('notificationDemoControls')) return;
        
        const controls = document.createElement('div');
        controls.id = 'notificationDemoControls';
        controls.innerHTML = `
            <div class="demo-controls-panel">
                <h6><i class="fas fa-cog"></i> Contrôles de Démonstration</h6>
                <div class="demo-buttons">
                    <button class="btn btn-sm btn-primary" id="startDemo">
                        <i class="fas fa-play"></i> Démarrer Demo
                    </button>
                    <button class="btn btn-sm btn-warning" id="pauseDemo">
                        <i class="fas fa-pause"></i> Pause
                    </button>
                    <button class="btn btn-sm btn-danger" id="stopDemo">
                        <i class="fas fa-stop"></i> Arrêter
                    </button>
                    <button class="btn btn-sm btn-info" id="nextDemo">
                        <i class="fas fa-forward"></i> Suivant
                    </button>
                </div>
                <div class="demo-info mt-2">
                    <small id="demoStatus">Prêt pour la démonstration</small>
                </div>
                <div class="demo-settings mt-2">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="soundToggle" checked>
                        <label class="form-check-label" for="soundToggle">
                            <i class="fas fa-volume-up"></i> Son
                        </label>
                    </div>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="vibrationToggle" checked>
                        <label class="form-check-label" for="vibrationToggle">
                            <i class="fas fa-mobile-alt"></i> Vibration
                        </label>
                    </div>
                </div>
            </div>
        `;
        
        // Styles pour les contrôles
        const styles = document.createElement('style');
        styles.textContent = `
            .demo-controls-panel {
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(255, 255, 255, 0.95);
                border: 1px solid #ddd;
                border-radius: 12px;
                padding: 15px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                backdrop-filter: blur(10px);
                z-index: 1000;
                min-width: 250px;
                font-size: 12px;
            }
            
            .demo-controls-panel h6 {
                margin: 0 0 10px 0;
                color: #333;
                font-weight: 600;
            }
            
            .demo-buttons {
                display: flex;
                gap: 5px;
                flex-wrap: wrap;
            }
            
            .demo-buttons .btn {
                flex: 1;
                min-width: 60px;
            }
            
            .demo-info {
                padding: 8px;
                background: #f8f9fa;
                border-radius: 6px;
                text-align: center;
            }
            
            .demo-settings {
                border-top: 1px solid #eee;
                padding-top: 10px;
            }
            
            .form-check {
                margin-bottom: 5px;
            }
            
            .form-check-label {
                font-size: 11px;
            }
            
            @media (max-width: 768px) {
                .demo-controls-panel {
                    top: 10px;
                    right: 10px;
                    left: 10px;
                    min-width: auto;
                }
                
                .demo-buttons .btn {
                    min-width: 50px;
                    font-size: 10px;
                }
            }
        `;
        document.head.appendChild(styles);
        
        // Ajouter les contrôles à la page
        document.body.appendChild(controls);
    }
    
    setupEventListeners() {
        document.getElementById('startDemo').addEventListener('click', () => {
            this.startDemo();
        });
        
        document.getElementById('pauseDemo').addEventListener('click', () => {
            this.pauseDemo();
        });
        
        document.getElementById('stopDemo').addEventListener('click', () => {
            this.stopDemo();
        });
        
        document.getElementById('nextDemo').addEventListener('click', () => {
            this.nextDemo();
        });
        
        document.getElementById('soundToggle').addEventListener('change', (e) => {
            this.toggleSound(e.target.checked);
        });
        
        document.getElementById('vibrationToggle').addEventListener('change', (e) => {
            this.toggleVibration(e.target.checked);
        });
    }
    
    startDemo() {
        this.updateStatus('Démonstration en cours...');
        this.currentDemo = 0;
        
        this.demoInterval = setInterval(() => {
            this.runCurrentDemo();
            this.currentDemo = (this.currentDemo + 1) % this.demos.length;
        }, 4000);
        
        // Démarrer immédiatement
        this.runCurrentDemo();
    }
    
    pauseDemo() {
        if (this.demoInterval) {
            clearInterval(this.demoInterval);
            this.demoInterval = null;
            this.updateStatus('Démonstration en pause');
        }
    }
    
    stopDemo() {
        if (this.demoInterval) {
            clearInterval(this.demoInterval);
            this.demoInterval = null;
        }
        
        this.currentDemo = 0;
        this.updateStatus('Démonstration arrêtée');
        
        // Cacher la notification
        if (window.hideAdvancedNotification) {
            window.hideAdvancedNotification();
        }
    }
    
    nextDemo() {
        this.runCurrentDemo();
        this.currentDemo = (this.currentDemo + 1) % this.demos.length;
    }
    
    runCurrentDemo() {
        const demo = this.demos[this.currentDemo];
        this.updateStatus(`${demo.name} - ${demo.description}`);
        
        // Utiliser la fonction avancée si disponible
        if (window.checkAdvancedNotification) {
            window.checkAdvancedNotification(demo.km, demo.nextKm, demo.vehicule);
        } else if (window.checkNotification) {
            // Fallback vers la fonction basique
            window.checkNotification(demo.km, demo.nextKm, demo.vehicule);
        }
        
        console.log(`Demo: ${demo.name}`, demo);
    }
    
    updateStatus(message) {
        const statusElement = document.getElementById('demoStatus');
        if (statusElement) {
            statusElement.textContent = message;
        }
    }
    
    toggleSound(enabled) {
        if (window.advancedNotificationSystem) {
            const currentState = window.advancedNotificationSystem.toggleSound();
            console.log('Sound toggled:', currentState);
        }
    }
    
    toggleVibration(enabled) {
        // Cette fonctionnalité pourrait être implémentée
        console.log('Vibration toggled:', enabled);
    }
    
    // Méthode pour tester des scénarios spécifiques
    testScenario(scenarioName) {
        const scenarios = {
            'maintenance_due': {
                km: 4950,
                nextKm: 5000,
                vehicule: 'TEST-001',
                description: 'Maintenance due dans 50km'
            },
            'overdue': {
                km: 5100,
                nextKm: 5000,
                vehicule: 'TEST-002',
                description: 'Maintenance en retard de 100km'
            },
            'multiple_vehicles': [
                { km: 4800, nextKm: 5000, vehicule: 'FLEET-001' },
                { km: 4900, nextKm: 5000, vehicule: 'FLEET-002' },
                { km: 4950, nextKm: 5000, vehicule: 'FLEET-003' }
            ]
        };
        
        const scenario = scenarios[scenarioName];
        if (scenario) {
            if (Array.isArray(scenario)) {
                // Scénario multi-véhicules
                scenario.forEach((vehicle, index) => {
                    setTimeout(() => {
                        if (window.checkAdvancedNotification) {
                            window.checkAdvancedNotification(vehicle.km, vehicle.nextKm, vehicle.vehicule);
                        }
                    }, index * 1000);
                });
            } else {
                // Scénario simple
                if (window.checkAdvancedNotification) {
                    window.checkAdvancedNotification(scenario.km, scenario.nextKm, scenario.vehicule);
                }
            }
        }
    }
}

// Initialisation automatique en mode développement
document.addEventListener('DOMContentLoaded', function() {
    // Vérifier si on est en mode développement (présence de certains paramètres)
    const urlParams = new URLSearchParams(window.location.search);
    const isDemoMode = urlParams.get('demo') === 'true' || 
                      window.location.hostname === 'localhost' ||
                      window.location.hostname === '127.0.0.1';
    
    if (isDemoMode) {
        console.log('Mode démonstration activé');
        window.notificationDemo = new NotificationDemo();
        
        // Ajouter des raccourcis clavier pour les développeurs
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.shiftKey) {
                switch(e.key) {
                    case 'D':
                        e.preventDefault();
                        window.notificationDemo.startDemo();
                        break;
                    case 'S':
                        e.preventDefault();
                        window.notificationDemo.stopDemo();
                        break;
                    case 'N':
                        e.preventDefault();
                        window.notificationDemo.nextDemo();
                        break;
                }
            }
        });
        
        // Message d'aide pour les développeurs
        console.log(`
🚀 Mode Démonstration Activé!

Raccourcis clavier:
- Ctrl+Shift+D : Démarrer la démo
- Ctrl+Shift+S : Arrêter la démo  
- Ctrl+Shift+N : Demo suivante

Scénarios de test disponibles:
- notificationDemo.testScenario('maintenance_due')
- notificationDemo.testScenario('overdue')
- notificationDemo.testScenario('multiple_vehicles')
        `);
    }
});
